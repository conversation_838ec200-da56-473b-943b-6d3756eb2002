@model CreateCheckDepositFormViewModel

@{
    ViewData["Title"] = "Add Check Deposit";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new Check Deposit
</h5>

<form asp-controller="CheckDeposit" id="myForm" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <input type="hidden" asp-for="SelectedChecksJson" value='@Html.Raw(Model.SelectedChecksJson)' />
    <div class="row">
        <div class="col-md-6 mt-2">
            <div class="form-group">
                <label asp-for="DepositDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="DepositDate" placeholder="Deposit Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="DepositDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="RefranceNumber" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="RefranceNumber" placeholder="Refrance Number">
                <span asp-validation-for="RefranceNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="BankId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="BankId"
                        data-placeholder="Select a bank" data-minimum-results-for-search="Infinity" onchange="handler.onBankChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.Banks)
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                </select>
                <span asp-validation-for="BankId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="BankAccountId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="BankAccountId"
                        data-placeholder="Select a bank account" data-minimum-results-for-search="Infinity">
                    <option value=""></option>
                    @foreach (var item in Model.BankAccounts.Where(x => x.BankId == Model.BankId))
                    {
                        <option value="@item.Id">@item.BankAccountCurrency - @item.BankAccountNumber</option>
                    }
                </select>
                <span asp-validation-for="BankAccountId" class="text-danger"></span>
            </div>
            <div class="container">
                <h5 class="mb-4">Select Checks To Deposit</h5>
                <div class="row">
                    <div class="col-md-5">
                        <h6>Available Checkes</h6>
                        <ul class="list-group" id="AvailableCheckes">
                            @foreach (CheckTreasuryVoucherModel checkTreasuryVoucher in Model.AllCheckTreasuryVouchers)
                            {
                                <li class="list-group-item d-flex justify-content-between align-items-start"
                                    data-item=@Html.Raw(JsonConvert.SerializeObject(checkTreasuryVoucher))>
                                    <div class="me-auto">
                                        <div class="fw-bold">@checkTreasuryVoucher.CheckNumber</div>
                                        <div>Issuer Name: <span class="text-success">@checkTreasuryVoucher.IssuerName</span></div>
                                        <div>Due Date: <span class="text-success">@checkTreasuryVoucher.DueDate.ToShortDateString()</span></div>
                                        <div>Amount: <span class="text-success">@checkTreasuryVoucher.Amount.ToString("N")</span></div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary move-right">→</button>
                                </li>
                            }
                        </ul>
                    </div>

                    <div class="col-md-2 d-flex flex-column justify-content-center align-items-center gap-2">
                        <button id="moveAllRight" type="button" class="btn btn-primary btn-sm">Move All →</button>
                        <button id="moveAllLeft" type="button" class="btn btn-secondary btn-sm">← Move All</button>
                    </div>

                    <div class="col-md-5">
                        <h6>Selected Checkes</h6>
                        <ul class="list-group" id="SelectedCheckes">
                            <!-- Items will be moved here -->
                        </ul>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-primary mt-4">Save</button>
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="~/js/tools.js"></script>
    <script type="module">
        import { CheckDepositHandler } from '/js/business/Treasury/checkDepositHandler.js';
        import { CheckDepositManager } from '/js/business/Treasury/checkDepositManager.js';

        const banks = @Html.Raw(Json.Serialize(Model.Banks));
        const bankAccounts = @Html.Raw(Json.Serialize(Model.BankAccounts));

        window.handler = new CheckDepositHandler(
            banks,
            bankAccounts
        );
    </script>
}
