﻿namespace SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckDepositController : BaseBusinessController<
        CheckDepositModel,
        CheckDepositModel,
        CreateCheckDepositViewModel,
        UpdateCheckDepositViewModel,
        IndexCheckDepositFormViewModel,
        CreateCheckDepositFormViewModel,
        UpdateCheckDepositFormViewModel>
    {
        private readonly ICheckDepositService _checkDepositService;

        public CheckDepositController(ICheckDepositService checkDepositService) : base(checkDepositService)
        {
            _checkDepositService = checkDepositService;
        }

        [FixCheckDeposit]
        public override async Task<IActionResult> Create(CreateCheckDepositFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FullFillViewAsync(model);

            try
            {
                var result = await _checkDepositService.AddAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkDeposit", ex.Message);
                return await FullFillViewAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        public override async Task<IActionResult> Update(Ulid id)
        {
            CheckDepositModel? entity = await _checkDepositService.GetByIdAsync(id);

            if (entity is null)
                return NotFound();

            JsonSerializerSettings jsonSerializerSettings = new JsonSerializerSettings()
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            };
            UpdateCheckDepositFormViewModel viewModel = new UpdateCheckDepositFormViewModel()
            {
                Id = entity.Id,
                DepositDate = entity.DepositDate,
                RefranceNumber = entity.RefranceNumber,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                SelectedCheckTreasuryVouchers = entity.CheckTreasuryVouchers.Select(x => x.Id).ToList(),
                Banks = await _checkDepositService.SelectiveBankListAsync().ToSelectListItemAsync(),
                BankAccounts = await _checkDepositService.SelectiveBankAccountListAsync().GetDataOrThrowIfNullAsync(),
                AllCheckTreasuryVouchers = await _checkDepositService.SelectiveCheckTreasuryVouchers().GetDataOrThrowIfNullAsync(),
                SelectedChecksJson = JsonConvert.SerializeObject(entity.CheckTreasuryVouchers, jsonSerializerSettings),
            };

            return View(viewModel);
        }

        [FixCheckDeposit]
        public override async Task<IActionResult> Update(UpdateCheckDepositFormViewModel model)
        {
            if (!ModelState.IsValid)
                return await FailedActionResultAsync(model);

            try
            {
                var result = await _checkDepositService.UpdateAsync(model);
                result.ThrowIfFailure();
            }
            catch (Exception ex)
            {
                ModelState.AddModelError("checkDeposit", ex.Message);
                return await FailedActionResultAsync(model);
            }
            return RedirectToAction(nameof(Index));
        }

        protected override async Task<IActionResult> FullFillViewAsync(CreateCheckDepositFormViewModel model)
        {
            model.Banks = await _checkDepositService.SelectiveBankListAsync().ToSelectListItemAsync();
            model.BankAccounts = await _checkDepositService.SelectiveBankAccountListAsync().GetDataOrThrowIfNullAsync();
            model.AllCheckTreasuryVouchers = await _checkDepositService.SelectiveCheckTreasuryVouchers().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }

        protected override async Task<IActionResult> FailedActionResultAsync(UpdateCheckDepositFormViewModel model)
        {
            model.Banks = await _checkDepositService.SelectiveBankListAsync().ToSelectListItemAsync();
            model.BankAccounts = await _checkDepositService.SelectiveBankAccountListAsync().GetDataOrThrowIfNullAsync();
            model.AllCheckTreasuryVouchers = await _checkDepositService.SelectiveCheckTreasuryVouchers().GetDataOrThrowIfNullAsync();
            return await Task.FromResult(View(model));
        }
    }
}
