﻿@model CreateCheckReturnFormViewModel

@{
    ViewData["Title"] = "Add Check Return";
}

<h5>
    <i class="bi bi-plus-circle-dotted"></i>
    Add a new Check Return
</h5>

<form asp-controller="CheckReturn" id="myForm" enctype="multipart/form-data">
    <div asp-validation-summary="All" class="text-danger" data-validation-summary="true"></div>
    <input type="hidden" asp-for="SelectedChecksJson" value='@Html.Raw(Model.SelectedChecksJson)' />
    <div class="row">
        <div class="col-md-6 mt-2">
            <div class="form-group">
                <label asp-for="ReturnDate" class="form-label mt-2"></label>
                <input type="date" class="form-control" asp-for="ReturnDate" placeholder="Return Date" value="@DateTime.Now.ToString("yyyy-MM-dd")">
                <span asp-validation-for="ReturnDate" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="RefranceNumber" class="form-label mt-2"></label>
                <input type="text" class="form-control" asp-for="RefranceNumber" placeholder="Refrance Number">
                <span asp-validation-for="RefranceNumber" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CheckVaultId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="CheckVaultId"
                        data-placeholder="Select a check vault" data-minimum-results-for-search="Infinity" onchange="handler.onCheckVaultChanged(this.value)">
                    <option value=""></option>
                    @foreach (var item in Model.CheckVaults)
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                </select>
                <span asp-validation-for="CheckVaultId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="CheckVaultLocationId" class="form-label mt-2"></label>
                <select class="form-select" asp-for="CheckVaultLocationId"
                        data-placeholder="Select a check vault location" data-minimum-results-for-search="Infinity">
                    <option value=""></option>
                    @foreach (var item in Model.CheckVaultLocations.Where(x => x.CheckVaultId == Model.CheckVaultId))
                    {
                        <option value="@item.Id">@item.CheckVaultLocationCurrency - @item.CheckVaultLocationNumber</option>
                    }
                </select>
                <span asp-validation-for="CheckVaultLocationId" class="text-danger"></span>
            </div>
            <div class="container">
                <h5 class="mb-4">Select Checks To Return</h5>
                <div class="row">
                    <div class="col-md-5">
                        <h6>Available Checkes</h6>
                        <ul class="list-group" id="AvailableCheckes">
                            @foreach (CheckTreasuryVoucherModel checkTreasuryVoucher in Model.AllCheckTreasuryVouchers)
                            {
                                <li class="list-group-item d-flex justify-content-between align-items-start"
                                    data-item=@Html.Raw(JsonConvert.SerializeObject(checkTreasuryVoucher))>
                                    <div class="me-auto">
                                        <div class="fw-bold">@checkTreasuryVoucher.CheckNumber</div>
                                        <div>Issuer Name: <span class="text-success">@checkTreasuryVoucher.IssuerName</span></div>
                                        <div>Due Date: <span class="text-success">@checkTreasuryVoucher.DueDate.ToShortDateString()</span></div>
                                        <div>Amount: <span class="text-success">@checkTreasuryVoucher.Amount.ToString("N")</span></div>
                                    </div>
                                    <button type="button" class="btn btn-sm btn-outline-primary move-right">→</button>
                                </li>
                            }
                        </ul>
                    </div>

                    <div class="col-md-2 d-flex flex-column justify-content-center align-items-center gap-2">
                        <button id="moveAllRight" type="button" class="btn btn-primary btn-sm">Move All →</button>
                        <button id="moveAllLeft" type="button" class="btn btn-secondary btn-sm">← Move All</button>
                    </div>

                    <div class="col-md-5">
                        <h6>Selected Checkes</h6>
                        <ul class="list-group" id="SelectedCheckes">
                            <!-- Items will be moved here -->
                        </ul>
                    </div>
                </div>
            </div>
            <button type="submit" class="btn btn-primary mt-4">Save</button>
        </div>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="~/js/tools.js"></script>
    <script type="module">
        import { CheckReturnHandler } from '/js/business/Treasury/checkReturnHandler.js';
        import { CheckReturnManager } from '/js/business/Treasury/checkReturnManager.js';
        const checkVaults = @Html.Raw(Json.Serialize(Model.CheckVaults));
        const checkVaultLocations = @Html.Raw(Json.Serialize(Model.CheckVaultLocations));

        window.handler = new CheckReturnHandler(
            checkVaults,
            checkVaultLocations);
    </script>
}
